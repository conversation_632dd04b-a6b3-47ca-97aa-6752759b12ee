<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'

// 语言状态管理 - 默认中文
const currentLang = ref('zh')

// 滚动动画相关
const observedElements = ref<Set<Element>>(new Set())

// 创建Intersection Observer来处理滚动动画
const createScrollObserver = () => {
  const observer = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add('animate-slide-up')
          entry.target.classList.remove('opacity-0', 'translate-y-8')
        }
      })
    },
    {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    }
  )

  return observer
}

// 初始化滚动观察器
let scrollObserver: IntersectionObserver | null = null

onMounted(() => {
  scrollObserver = createScrollObserver()

  // 观察所有需要动画的元素
  const elementsToAnimate = document.querySelectorAll('.scroll-animate')
  elementsToAnimate.forEach((el) => {
    el.classList.add('opacity-0', 'translate-y-8', 'transition-all', 'duration-700', 'ease-out')
    scrollObserver?.observe(el)
    observedElements.value.add(el)
  })

  // 处理主横幅图片加载动画
  const heroImages = document.querySelectorAll('.hero-image')
  heroImages.forEach((img) => {
    const imageElement = img as HTMLImageElement

    // 如果图片已经加载完成
    if (imageElement.complete && imageElement.naturalWidth > 0) {
      setTimeout(() => {
        imageElement.classList.add('loaded')
      }, 800) // 让用户看到一些加载动画
    } else {
      // 监听图片加载完成事件
      imageElement.addEventListener('load', () => {
        setTimeout(() => {
          imageElement.classList.add('loaded')
        }, 500) // 加载完成后稍微延迟显示
      })

      // 防止图片加载失败时一直显示占位符
      imageElement.addEventListener('error', () => {
        imageElement.classList.add('loaded')
      })
    }
  })
})

onUnmounted(() => {
  scrollObserver?.disconnect()
})

// 多语言翻译对象
const translations = {
  zh: {
    // 导航栏
    switchToEnglish: '中文',
    brandName: 'Relax',
    
    // 主横幅区域
    editorChoice: '编辑推荐',
    appStore: 'Apple App Store',
    mainTitle: 'Relax 健康',
    subtitle: '经典焕新演绎',
    
    // Tab功能导航区域
    intro1: '你是否常常感到疲惫、焦虑、难以集中精力，或者发现自己很难坚持健康的习惯？',
    intro2: 'Relax App应运而生，致力于帮助您深度了解自身压力状况，并通过科学、个性化的方式，逐步培养起有益身心的健康习惯。我们相信，真正的健康，源于对压力的有效管理和积极生活方式的养成。',
    
    // 产品介绍区域
    healthPartner: '您的健康好搭子',
    healthPartnerDesc: 'Relax不仅仅是一个监测工具，更是您的专属健康教练和陪伴者，助您找回内心的宁静与活力',
    
    // 健康监测区域
    timelyMonitoring: '及时监测',
    healthAtReach: '让健康触手可及',
    monitoringDesc: '通过HRV实时监测，追踪您的压力指数，为您提供精准的压力趋势报告和深层洞察，让您看见压力的起伏',
    
    // 数据分析区域
    comprehensiveInsight: '全面洞察',
    notJustData: '不仅是数据',
    insightDesc: '通过智能分析，确保每项功能和建议都具备科学依据，真正帮助到您为您解读数据背后的身心状态。',
    
    // 情绪监测区域
    emotionBehavior: '情绪与行为',
    insightDiary: '洞察日记',
    emotionDesc: '记录每一次心跳的变化，解读情绪背后的故事。通过科学的数据分析，让您更好地理解自己的情绪模式，掌握心理健康的主动权',
    
    // 下载区域
    downloadRelax: '下载Relax'
  },
  en: {
    // 导航栏
    switchToEnglish: 'English',
    brandName: 'Relax',
    
    // 主横幅区域
    editorChoice: "Editor's Choice",
    appStore: 'Apple App Store',
    mainTitle: 'Relax Health',
    subtitle: 'Reimagined',
    
    // Tab功能导航区域
    intro1: 'Do you often feel tired, anxious, unable to concentrate, or find it difficult to maintain healthy habits?',
    intro2: 'Relax App was born to help you deeply understand your stress conditions and gradually develop beneficial physical and mental health habits through scientific and personalized methods. We believe that true health comes from effective stress management and the development of positive lifestyle habits.',
    
    // 产品介绍区域
    healthPartner: 'Your Health Companion',
    healthPartnerDesc: 'Relax is not just a monitoring tool, but your dedicated health coach and companion, helping you regain inner peace and vitality',
    
    // 健康监测区域
    timelyMonitoring: 'Real-time Monitoring',
    healthAtReach: 'Health at Your Fingertips',
    monitoringDesc: 'Through HRV real-time monitoring, track your stress index, provide you with accurate stress trend reports and deep insights, let you see the fluctuations of stress',
    
    // 数据分析区域
    comprehensiveInsight: 'Comprehensive Insights',
    notJustData: 'Beyond Just Data',
    insightDesc: 'Through intelligent analysis, ensure that every function and recommendation is scientifically based, truly helping you interpret the physical and mental state behind the data.',
    
    // 情绪监测区域
    emotionBehavior: 'Emotion & Behavior',
    insightDiary: 'Insight Journal',
    emotionDesc: 'Record every heartbeat change and interpret the story behind emotions. Through scientific data analysis, help you better understand your emotional patterns and take control of your mental health',
    
    // 下载区域
    downloadRelax: 'Download Relax'
  }
}

// 计算属性获取当前语言的翻译
const t = computed(() => translations[currentLang.value as keyof typeof translations])

// 语言切换函数
const toggleLanguage = () => {
  currentLang.value = currentLang.value === 'zh' ? 'en' : 'zh'
  console.log('语言切换到:', currentLang.value === 'zh' ? '中文' : 'English')
}
</script>

<template>
  <div class="w-full">
    <!-- 顶部导航区域 -->
    <header class="w-full h-16 bg-[#E9F4FD]">
      <div class="max-w-7xl mx-auto px-4 h-full flex items-center justify-between">
                <!-- 左侧：Logo + 品牌名 -->
        <div class="flex items-center space-x-3">
          <img src="@/assets/logo.png" alt="Relax Logo" class="h-8 w-auto">
          <span class="text-xl font-semibold text-gray-800">{{ t.brandName }}</span>
        </div>
        
        <!-- 右侧：语言切换按钮 -->
        <button @click="toggleLanguage"
          class="flex items-center space-x-2 text-gray-600 hover:text-blue-600 active:text-blue-800 transition-colors duration-200">
          <img src="@/assets/lang.svg" alt="Language" class="h-5 w-5">
          <span class="text-sm font-medium">{{ t.switchToEnglish }}</span>
        </button>
      </div>
    </header>

    <!-- 主横幅区域 -->
    <section class="w-full h-[500px] md:h-[700px] relative bg-[#E9F4FD] flex items-start md:items-center pt-8 md:pt-0">
      <div class="max-w-7xl mx-auto px-4 w-full">
        <!-- 左侧内容区 -->
        <div class="w-full md:w-[250px] text-center md:text-left animate-fade-in relative z-20">
          <!-- 编辑推荐标签 - 背景图片 -->
          <div class="relative mb-4 md:mb-6 text-center">
            <img src="@/assets/1-editor.png" alt="编辑推荐背景" class="w-[180px] md:w-[220px] h-auto mx-auto">
            <div class="absolute inset-0 flex flex-col items-center justify-center text-center">
              <div class="text-sm md:text-lg text-gray-800 font-medium">{{ t.editorChoice }}</div>
              <div class="text-xs md:text-sm text-gray-600">{{ t.appStore }}</div>
            </div>
          </div>

          <!-- 文字区 -->
          <div class="mb-6 md:mb-8 text-center">
            <h1 :class="[
              'font-bold text-gray-800 mb-3 md:mb-4 leading-tight',
              currentLang === 'zh' ? 'text-3xl md:text-5xl' : 'text-2xl md:text-4xl'
            ]">
              {{ t.mainTitle }}
            </h1>
            <p :class="[
              'text-gray-600 leading-relaxed',
              currentLang === 'zh' ? 'text-xl md:text-[32px]' : 'text-lg md:text-2xl'
            ]">
              {{ t.subtitle }}
            </p>
          </div>

          <!-- 下载按钮 -->
          <div class="flex justify-center relative z-10">
            <a href="https://apps.apple.com/cn/app/relax-%E4%BD%A0%E7%9A%84%E5%8E%8B%E5%8A%9B%E7%9B%91%E6%B5%8B%E5%A5%BD%E6%90%AD%E5%AD%90/id6745132720"
               target="_blank"
               rel="noopener noreferrer"
               class="inline-block relative z-10">
              <img src="@/assets/Link.png" alt="下载按钮"
                class="w-[150px] md:w-[180px] h-auto cursor-pointer hover:opacity-90 hover:scale-105 transition-all duration-300 ease-out">
            </a>
          </div>
        </div>
      </div>

      <!-- 背景图片 - 贴底，桌面端右对齐，移动端居中超出显示 -->
      <div class="absolute bottom-0 left-0 w-full">
        <!-- 桌面端显示 -->
        <div class="hidden md:block max-w-7xl mx-auto px-4">
          <div class="flex justify-end">
            <img src="@/assets/1-bottom.jpg" alt="背景装饰" class="w-[1000px] h-auto hero-image" loading="lazy">
          </div>
        </div>
        <!-- 移动端显示 - 左右超出1/5 -->
        <div class="block md:hidden flex justify-center">
          <img src="@/assets/1-bottom.jpg" alt="背景装饰" class="w-[140vw] h-auto hero-image" loading="lazy">
        </div>
      </div>
    </section>

    <!-- Tab功能导航区域 -->
    <section class="w-full bg-white py-12 md:py-16">
      <div class="max-w-7xl mx-auto px-4 text-center scroll-animate">
        <!-- 图标 -->
        <div class="mb-6 md:mb-8">
          <img src="@/assets/2-center.svg" alt="Health Icon"
            class="w-[120px] h-[120px] md:w-[150px] md:h-[150px] mx-auto" loading="lazy">
        </div>

                <!-- 文字内容 -->
        <div class="space-y-4 md:space-y-6">
          <!-- 第一段 -->
          <p class="text-sm md:text-base text-gray-700 leading-relaxed px-4 md:px-0">
            {{ t.intro1 }}
          </p>
          
          <!-- 第二段 -->
          <p class="text-sm md:text-base text-gray-700 leading-relaxed px-4 md:px-0">
            {{ t.intro2 }}
          </p>
        </div>
      </div>
    </section>

    <!-- 产品介绍区域 -->
    <section class="w-full h-[500px] md:h-[600px] relative bg-[#E9F4FD] overflow-hidden">
      <div class="max-w-7xl mx-auto px-4 text-center">
        <!-- 文字内容区域 - 应用滚动动画 -->
        <div class="scroll-animate">
          <!-- 标题 - 距离顶部80px -->
          <h2 class="text-2xl md:text-4xl font-medium text-gray-800 pt-16 md:pt-20 mb-4 md:mb-6 leading-tight">
            {{ t.healthPartner }}
          </h2>

          <!-- 文字描述 - 距离标题48px -->
          <p class="text-sm md:text-base font-normal text-gray-700 leading-relaxed px-4 md:px-0">
            {{ t.healthPartnerDesc }}
          </p>
        </div>

        <!-- 图片 - 贴底定位，不应用滚动动画 -->
        <!-- 桌面端显示 -->
        <div class="hidden md:block absolute left-1/2 transform -translate-x-1/2 bottom-0 max-w-7xl w-full px-4">
          <img src="@/assets/3-center.png" alt="健康好搭子" class="max-w-full h-auto mx-auto" loading="lazy">
        </div>
        <!-- 移动端显示 - 左右超出一半以上 -->
        <div class="block md:hidden absolute left-1/2 transform -translate-x-1/2 bottom-0 w-[250vw] h-[300px]">
          <img src="@/assets/3-center.png" alt="健康好搭子" class="w-full h-full object-cover object-bottom" loading="lazy">
        </div>
      </div>
    </section>


    <!-- 健康监测区域 -->
    <section class="w-full h-auto md:h-[600px] bg-white flex items-center justify-center py-12 md:py-0">
      <div class="max-w-7xl mx-auto px-4">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 md:gap-12 items-center scroll-animate">
          <!-- 左侧图片 -->
          <div class="flex justify-center lg:justify-end order-2 lg:order-1">
            <img src="@/assets/4-left.png" alt="及时监测" class="max-w-full h-auto" loading="lazy">
          </div>

                    <!-- 右侧内容 -->
          <div class="text-center lg:text-left order-1 lg:order-2">
            <!-- 标题 -->
            <h2 class="text-2xl md:text-4xl font-medium text-gray-800 mb-4 md:mb-6 leading-tight">
              {{ t.timelyMonitoring }}<br>{{ t.healthAtReach }}
            </h2>
            
            <!-- 正文 -->
            <p class="text-sm md:text-base text-gray-700 leading-relaxed max-w-[540px] mx-auto lg:mx-0">
              {{ t.monitoringDesc }}
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- 数据分析区域 -->
    <section class="w-full h-auto md:h-[600px] bg-[#E9F4FD] flex items-center justify-center py-12 md:py-0">
      <div class="max-w-7xl mx-auto px-4">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 md:gap-12 items-center scroll-animate">
                    <!-- 左侧内容 -->
          <div class="text-center lg:text-left order-1 lg:order-1">
            <!-- 标题 -->
            <h2 class="text-2xl md:text-4xl font-medium text-gray-800 mb-4 md:mb-6 leading-tight">
              {{ t.comprehensiveInsight }}<br>{{ t.notJustData }}
            </h2>
            
            <!-- 正文 -->
            <p class="text-sm md:text-base text-gray-700 leading-relaxed max-w-[540px] mx-auto lg:mx-0">
              {{ t.insightDesc }}
            </p>
          </div>

          <!-- 右侧图片 -->
          <div class="flex justify-center lg:justify-start order-2 lg:order-2">
            <img src="@/assets/5-right.png" alt="全面洞察" class="max-w-full h-auto" loading="lazy">
          </div>
        </div>
      </div>
    </section>

    <!-- 情绪监测区域 -->
    <section class="w-full h-[600px] bg-white">
      <div class="max-w-7xl mx-auto px-4 h-full">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 md:gap-12 h-full scroll-animate">
          <!-- 左侧图片 -->
          <div class="flex justify-center lg:justify-end items-end lg:items-center order-2 lg:order-1">
            <img src="@/assets/6-left.png" alt="情绪与行为洞察日记" class="max-w-full h-auto" loading="lazy">
          </div>

          <!-- 右侧内容 -->
          <div class="flex items-start lg:items-center pt-12 lg:pt-0 order-1 lg:order-2 w-full">
                         <div class="w-full text-center lg:text-left">
               <!-- 标题 -->
               <h2 class="text-2xl md:text-4xl font-medium text-gray-800 leading-tight mb-4 lg:mb-6">
                 {{ t.emotionBehavior }}<br>{{ t.insightDiary }}
               </h2>

               <!-- 描述文字 - 参考你之前的代码风格，简洁一句话 -->
               <p class="text-sm md:text-base font-normal text-gray-700 leading-relaxed px-4 md:px-0">
                 {{ t.emotionDesc }}
               </p>
             </div>
          </div>

        </div>
      </div>
    </section>


    <!-- 下载区域 -->
    <section class="w-full h-80 md:h-96 bg-[#FAFDFF] flex items-center justify-center">
      <div class="text-center px-4 scroll-animate">
        <!-- Logo -->
        <div class="mb-4 md:mb-6">
          <img src="@/assets/logo.png" alt="Relax Logo" class="mx-auto h-12 md:h-auto">
        </div>

        <!-- 标题 -->
        <h2 class="text-2xl md:text-4xl font-medium text-gray-800 mb-6 md:mb-8">{{ t.downloadRelax }}</h2>

        <!-- 下载按钮 -->
        <div class="flex justify-center">
          <a href="https://apps.apple.com/cn/app/relax-%E4%BD%A0%E7%9A%84%E5%8E%8B%E5%8A%9B%E7%9B%91%E6%B5%8B%E5%A5%BD%E6%90%AD%E5%AD%90/id6745132720"
            target="_blank"
            rel="noopener noreferrer">
            <img src="@/assets/Link.png" alt="下载按钮"
              class="w-[180px] h-auto cursor-pointer hover:opacity-90 hover:scale-105 transition-all duration-300 ease-out">
          </a>
        </div>
        
        <!-- 备案信息 -->
        <div class="mt-12 text-center text-gray-500 text-sm">
          <p>
            <a
              href="https://beian.miit.gov.cn/"
              target="_blank"
              rel="noopener noreferrer"
              class="hover:text-blue-600 transition-colors"
            >
              鲁ICP备19064358号-8
            </a>
          </p>
        </div>
      </div>
    </section>
  </div>
</template>

<style scoped>
/* 主横幅区域图片加载动画 */
.hero-image {
  opacity: 0;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  min-height: 200px;
  transition: opacity 0.8s ease-in-out;
}

.hero-image.loaded {
  opacity: 1;
  background: none;
  animation: none;
}

@keyframes shimmer {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 增强按钮悬停效果 */
.hover\:scale-105:hover {
  transform: scale(1.05);
}

/* 滚动动画增强 */
.scroll-animate {
  transition: all 0.7s ease-out;
}

.scroll-animate.opacity-0 {
  opacity: 0;
  transform: translateY(30px);
}

/* 滚动动画元素内的图片不应用额外的透明度动画 */
.scroll-animate img {
  opacity: 1 !important;
  transition: none;
}
</style>
